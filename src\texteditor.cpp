#include "texteditor.h"
#include <QApplication>
#include <QTextCursor>
#include <QTextBlock>

TextEditor::TextEditor(QWidget *parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_textEdit(nullptr)
    , m_status<PERSON><PERSON>e(nullptr)
    , m_status<PERSON>abel(nullptr)
    , m_position<PERSON>abel(nullptr)
    , m_fileType<PERSON>abel(nullptr)
    , m_isModified(false)
{
    setupUI();
}

void TextEditor::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setSpacing(0);
    m_mainLayout->setContentsMargins(0, 0, 0, 0);
    
    setupEditor();
    setupStatusBar();
}

void TextEditor::setupEditor()
{
    m_textEdit = new QTextEdit(this);
    
    // Set up font
    QFont font("Consolas", 11);
    if (!font.exactMatch()) {
        font.setFamily("Courier New");
    }
    font.setFixedPitch(true);
    m_textEdit->setFont(font);
    
    // Set up editor properties
    m_textEdit->setLineWrapMode(QTextEdit::NoWrap);
    m_textEdit->setTabStopDistance(40); // 4 spaces equivalent
    
    // Style the editor
    m_textEdit->setStyleSheet(
        "QTextEdit {"
        "    background-color: #ffffff;"
        "    color: #2c3e50;"
        "    border: none;"
        "    selection-background-color: #3498db;"
        "    selection-color: white;"
        "}"
    );
    
    // Connect signals
    connect(m_textEdit, &QTextEdit::textChanged, this, &TextEditor::onTextChanged);
    connect(m_textEdit, &QTextEdit::cursorPositionChanged, this, &TextEditor::updateLineNumberArea);
    
    m_mainLayout->addWidget(m_textEdit);
}

void TextEditor::setupStatusBar()
{
    m_statusFrame = new QFrame(this);
    m_statusFrame->setFrameStyle(QFrame::StyledPanel | QFrame::Raised);
    m_statusFrame->setMaximumHeight(25);
    m_statusFrame->setStyleSheet(
        "QFrame {"
        "    background-color: #ecf0f1;"
        "    border-top: 1px solid #bdc3c7;"
        "    padding: 2px;"
        "}"
    );
    
    QHBoxLayout *statusLayout = new QHBoxLayout(m_statusFrame);
    statusLayout->setContentsMargins(10, 2, 10, 2);
    statusLayout->setSpacing(20);
    
    // Status label (file info)
    m_statusLabel = new QLabel("Ready", m_statusFrame);
    m_statusLabel->setStyleSheet("color: #7f8c8d; font-size: 11px;");
    
    // File type label
    m_fileTypeLabel = new QLabel("Plain Text", m_statusFrame);
    m_fileTypeLabel->setStyleSheet("color: #7f8c8d; font-size: 11px;");
    
    // Position label
    m_positionLabel = new QLabel("Line 1, Column 1", m_statusFrame);
    m_positionLabel->setStyleSheet("color: #7f8c8d; font-size: 11px;");
    
    statusLayout->addWidget(m_statusLabel);
    statusLayout->addStretch();
    statusLayout->addWidget(m_fileTypeLabel);
    statusLayout->addWidget(m_positionLabel);
    
    m_mainLayout->addWidget(m_statusFrame);
}

void TextEditor::loadFromFile(const QString &filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QMessageBox::warning(this, "Error", 
            QString("Could not open file: %1\n%2").arg(filePath, file.errorString()));
        return;
    }
    
    QTextStream in(&file);
    QString content = in.readAll();
    file.close();
    
    m_textEdit->setPlainText(content);
    m_currentFilePath = filePath;
    m_fileType = detectFileType(filePath);
    m_isModified = false;
    
    updateStatusBar();
    
    // Move cursor to beginning
    QTextCursor cursor = m_textEdit->textCursor();
    cursor.movePosition(QTextCursor::Start);
    m_textEdit->setTextCursor(cursor);
}

void TextEditor::saveToFile(const QString &filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QMessageBox::warning(this, "Error", 
            QString("Could not save file: %1\n%2").arg(filePath, file.errorString()));
        return;
    }
    
    QTextStream out(&file);
    out << m_textEdit->toPlainText();
    file.close();
    
    m_currentFilePath = filePath;
    m_fileType = detectFileType(filePath);
    m_isModified = false;
    
    updateStatusBar();
}

void TextEditor::clear()
{
    m_textEdit->clear();
    m_currentFilePath.clear();
    m_fileType = "Plain Text";
    m_isModified = false;
    updateStatusBar();
}

bool TextEditor::isModified() const
{
    return m_isModified;
}

QString TextEditor::getCurrentText() const
{
    return m_textEdit->toPlainText();
}

void TextEditor::setText(const QString &text)
{
    m_textEdit->setPlainText(text);
    m_isModified = false;
    updateStatusBar();
}

void TextEditor::onTextChanged()
{
    if (!m_isModified) {
        m_isModified = true;
        emit modificationChanged(true);
        updateStatusBar();
    }
    emit textChanged();
}

void TextEditor::updateLineNumberArea()
{
    QTextCursor cursor = m_textEdit->textCursor();
    int line = cursor.blockNumber() + 1;
    int column = cursor.columnNumber() + 1;
    
    m_positionLabel->setText(QString("Line %1, Column %2").arg(line).arg(column));
}

QString TextEditor::detectFileType(const QString &filePath)
{
    QFileInfo fileInfo(filePath);
    QString suffix = fileInfo.suffix().toLower();
    
    if (suffix == "cpp" || suffix == "cxx" || suffix == "cc") {
        return "C++";
    } else if (suffix == "h" || suffix == "hpp" || suffix == "hxx") {
        return "C++ Header";
    } else if (suffix == "c") {
        return "C";
    } else if (suffix == "py") {
        return "Python";
    } else if (suffix == "js") {
        return "JavaScript";
    } else if (suffix == "html" || suffix == "htm") {
        return "HTML";
    } else if (suffix == "css") {
        return "CSS";
    } else if (suffix == "xml") {
        return "XML";
    } else if (suffix == "json") {
        return "JSON";
    } else if (suffix == "txt") {
        return "Text";
    } else if (suffix == "md") {
        return "Markdown";
    } else {
        return "Plain Text";
    }
}

void TextEditor::updateStatusBar()
{
    QString statusText;
    if (m_currentFilePath.isEmpty()) {
        statusText = m_isModified ? "New File (Modified)" : "New File";
    } else {
        QFileInfo fileInfo(m_currentFilePath);
        statusText = fileInfo.fileName();
        if (m_isModified) {
            statusText += " (Modified)";
        }
    }
    
    m_statusLabel->setText(statusText);
    m_fileTypeLabel->setText(m_fileType);
    
    // Update position
    updateLineNumberArea();
}
