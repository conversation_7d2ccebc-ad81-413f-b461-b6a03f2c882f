#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QStackedWidget>
#include <QMenuBar>
#include <QAction>
#include <QFileDialog>
#include <QMessageBox>

class WelcomeScreen;
class TextEditor;

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void openFile();
    void openProject();
    void newFile();
    void saveFile();
    void saveAsFile();
    void exitApplication();
    void showAbout();
    void onFileOpened(const QString &filePath);
    void showWelcomeScreen();

private:
    void setupMenuBar();
    void setupActions();
    void setupUI();
    
    // UI Components
    QStackedWidget *m_stackedWidget;
    WelcomeScreen *m_welcomeScreen;
    TextEditor *m_textEditor;
    
    // Menu Actions
    QAction *m_newAction;
    QAction *m_openAction;
    QAction *m_openProjectAction;
    QAction *m_saveAction;
    QAction *m_saveAsAction;
    QAction *m_exitAction;
    QAction *m_aboutAction;
    
    // Current file tracking
    QString m_currentFilePath;
    bool m_isModified;
};

#endif // MAINWINDOW_H
