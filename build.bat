@echo off
echo Building Velocity Code Editor...
echo.

REM Check if Qt is in PATH
where qmake >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Error: qmake not found in PATH
    echo Please ensure Qt is installed and added to your PATH
    echo Or open this project in Qt Creator instead
    pause
    exit /b 1
)

REM Create build directory
if not exist build mkdir build
cd build

REM Generate Makefile
echo Generating Makefile...
qmake ..\Velocity.pro

REM Build the project
echo Building project...
if exist Makefile (
    nmake
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo Build successful!
        echo Executable location: build\bin\Velocity.exe
    ) else (
        echo Build failed!
    )
) else (
    echo Failed to generate Makefile
)

cd ..
pause
