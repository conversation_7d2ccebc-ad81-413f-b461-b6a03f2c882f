#include "welcomescreen.h"
#include <QFont>
#include <QSizePolicy>
#include <QPalette>
#include <QApplication>

WelcomeScreen::WelcomeScreen(QWidget *parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_title<PERSON>abel(nullptr)
    , m_header<PERSON>rame(nullptr)
    , m_contentFrame(nullptr)
    , m_openFileButton(nullptr)
    , m_openProjectButton(nullptr)
    , m_tipsTextEdit(nullptr)
    , m_tipsScrollArea(nullptr)
{
    setupUI();
}

void WelcomeScreen::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setSpacing(20);
    m_mainLayout->setContentsMargins(40, 40, 40, 40);
    
    setupHeader();
    setupFileOperations();
    setupTipsSection();
    
    // Add stretch to center content vertically
    m_mainLayout->addStretch();
}

void WelcomeScreen::setupHeader()
{
    m_headerFrame = new QFrame(this);
    m_headerFrame->setFrameStyle(QFrame::NoFrame);
    
    QVBoxLayout *headerLayout = new QVBoxLayout(m_headerFrame);
    headerLayout->setAlignment(Qt::AlignCenter);
    
    // Title label
    m_titleLabel = new QLabel("Velocity", m_headerFrame);
    QFont titleFont = m_titleLabel->font();
    titleFont.setPointSize(36);
    titleFont.setBold(true);
    m_titleLabel->setFont(titleFont);
    m_titleLabel->setAlignment(Qt::AlignCenter);
    m_titleLabel->setStyleSheet("color: #2c3e50; margin: 20px;");
    
    // Subtitle
    QLabel *subtitleLabel = new QLabel("Modern Code Editor", m_headerFrame);
    QFont subtitleFont = subtitleLabel->font();
    subtitleFont.setPointSize(14);
    subtitleLabel->setFont(subtitleFont);
    subtitleLabel->setAlignment(Qt::AlignCenter);
    subtitleLabel->setStyleSheet("color: #7f8c8d; margin-bottom: 30px;");
    
    headerLayout->addWidget(m_titleLabel);
    headerLayout->addWidget(subtitleLabel);
    
    m_mainLayout->addWidget(m_headerFrame);
}

void WelcomeScreen::setupFileOperations()
{
    m_contentFrame = new QFrame(this);
    m_contentFrame->setFrameStyle(QFrame::StyledPanel);
    m_contentFrame->setStyleSheet("QFrame { background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; }");
    
    QVBoxLayout *contentLayout = new QVBoxLayout(m_contentFrame);
    contentLayout->setSpacing(15);
    contentLayout->setContentsMargins(30, 30, 30, 30);
    
    // Section title
    QLabel *sectionTitle = new QLabel("Get Started", m_contentFrame);
    QFont sectionFont = sectionTitle->font();
    sectionFont.setPointSize(16);
    sectionFont.setBold(true);
    sectionTitle->setFont(sectionFont);
    sectionTitle->setStyleSheet("color: #2c3e50; margin-bottom: 10px;");
    
    // File operations buttons
    QHBoxLayout *buttonsLayout = new QHBoxLayout();
    buttonsLayout->setSpacing(20);
    
    m_openFileButton = new QPushButton("Open File", m_contentFrame);
    m_openFileButton->setMinimumSize(120, 40);
    m_openFileButton->setStyleSheet(
        "QPushButton {"
        "    background-color: #3498db;"
        "    color: white;"
        "    border: none;"
        "    border-radius: 6px;"
        "    font-size: 14px;"
        "    font-weight: bold;"
        "    padding: 8px 16px;"
        "}"
        "QPushButton:hover {"
        "    background-color: #2980b9;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #21618c;"
        "}"
    );
    connect(m_openFileButton, &QPushButton::clicked, this, &WelcomeScreen::onOpenFileClicked);
    
    m_openProjectButton = new QPushButton("Open Project", m_contentFrame);
    m_openProjectButton->setMinimumSize(120, 40);
    m_openProjectButton->setStyleSheet(
        "QPushButton {"
        "    background-color: #27ae60;"
        "    color: white;"
        "    border: none;"
        "    border-radius: 6px;"
        "    font-size: 14px;"
        "    font-weight: bold;"
        "    padding: 8px 16px;"
        "}"
        "QPushButton:hover {"
        "    background-color: #229954;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #1e8449;"
        "}"
    );
    connect(m_openProjectButton, &QPushButton::clicked, this, &WelcomeScreen::onOpenProjectClicked);
    
    buttonsLayout->addWidget(m_openFileButton);
    buttonsLayout->addWidget(m_openProjectButton);
    buttonsLayout->addStretch();
    
    contentLayout->addWidget(sectionTitle);
    contentLayout->addLayout(buttonsLayout);
    
    m_mainLayout->addWidget(m_contentFrame);
}

void WelcomeScreen::setupTipsSection()
{
    // Tips section frame
    QFrame *tipsFrame = new QFrame(this);
    tipsFrame->setFrameStyle(QFrame::StyledPanel);
    tipsFrame->setStyleSheet("QFrame { background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; }");
    
    QVBoxLayout *tipsLayout = new QVBoxLayout(tipsFrame);
    tipsLayout->setSpacing(15);
    tipsLayout->setContentsMargins(30, 30, 30, 30);
    
    // Tips title
    QLabel *tipsTitle = new QLabel("Tips & Features", tipsFrame);
    QFont tipsFont = tipsTitle->font();
    tipsFont.setPointSize(16);
    tipsFont.setBold(true);
    tipsTitle->setFont(tipsFont);
    tipsTitle->setStyleSheet("color: #2c3e50; margin-bottom: 10px;");
    
    // Tips content
    m_tipsTextEdit = new QTextEdit(tipsFrame);
    m_tipsTextEdit->setReadOnly(true);
    m_tipsTextEdit->setMaximumHeight(200);
    m_tipsTextEdit->setStyleSheet(
        "QTextEdit {"
        "    background-color: white;"
        "    border: 1px solid #ced4da;"
        "    border-radius: 4px;"
        "    padding: 10px;"
        "    font-size: 13px;"
        "    line-height: 1.4;"
        "}"
    );
    m_tipsTextEdit->setHtml(getTipsContent());
    
    tipsLayout->addWidget(tipsTitle);
    tipsLayout->addWidget(m_tipsTextEdit);
    
    m_mainLayout->addWidget(tipsFrame);
}

QString WelcomeScreen::getTipsContent()
{
    return QString(
        "<div style='font-family: Arial, sans-serif; line-height: 1.6;'>"
        "<p><strong>🚀 Getting Started:</strong></p>"
        "<ul>"
        "<li>Use <strong>Ctrl+O</strong> to quickly open files</li>"
        "<li>Access the File menu for all file operations</li>"
        "<li>The editor supports various file types including text, C++, and Python files</li>"
        "</ul>"
        
        "<p><strong>📁 File Management:</strong></p>"
        "<ul>"
        "<li>Open individual files or browse project directories</li>"
        "<li>Use <strong>Ctrl+S</strong> to save your work</li>"
        "<li>Use <strong>Ctrl+Shift+S</strong> for Save As functionality</li>"
        "</ul>"
        
        "<p><strong>⌨️ Keyboard Shortcuts:</strong></p>"
        "<ul>"
        "<li><strong>Ctrl+N</strong> - Create new file</li>"
        "<li><strong>Ctrl+O</strong> - Open file</li>"
        "<li><strong>Ctrl+S</strong> - Save file</li>"
        "<li><strong>Ctrl+Q</strong> - Exit application</li>"
        "</ul>"
        
        "<p><strong>💡 Pro Tips:</strong></p>"
        "<ul>"
        "<li>The interface is designed to be clean and distraction-free</li>"
        "<li>Menu bar provides access to all major functions</li>"
        "<li>File operations are accessible both from buttons and menu items</li>"
        "</ul>"
        "</div>"
    );
}

void WelcomeScreen::onOpenFileClicked()
{
    emit openFileRequested();
}

void WelcomeScreen::onOpenProjectClicked()
{
    emit openProjectRequested();
}
