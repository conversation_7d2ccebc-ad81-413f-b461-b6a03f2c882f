#!/bin/bash

echo "Building Velocity Code Editor..."
echo

# Check if qmake is available
if ! command -v qmake &> /dev/null; then
    echo "Error: qmake not found"
    echo "Please ensure Qt is installed and qmake is in your PATH"
    echo "Or use Qt Creator to open this project instead"
    exit 1
fi

# Create build directory
mkdir -p build
cd build

# Generate Makefile
echo "Generating Makefile..."
qmake ../Velocity.pro

# Build the project
echo "Building project..."
if [ -f Makefile ]; then
    make
    if [ $? -eq 0 ]; then
        echo
        echo "Build successful!"
        echo "Executable location: build/bin/Velocity"
    else
        echo "Build failed!"
        exit 1
    fi
else
    echo "Failed to generate Makefile"
    exit 1
fi

cd ..
echo "Done!"
