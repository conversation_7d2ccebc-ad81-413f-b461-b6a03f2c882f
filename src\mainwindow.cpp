#include "mainwindow.h"
#include "welcomescreen.h"
#include "texteditor.h"
#include <QApplication>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QSplitter>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_stackedWidget(nullptr)
    , m_welcomeScreen(nullptr)
    , m_textEditor(nullptr)
    , m_isModified(false)
{
    setWindowTitle("Velocity");
    setMinimumSize(800, 600);
    resize(1200, 800);
    
    setupActions();
    setupMenuBar();
    setupUI();
}

MainWindow::~MainWindow()
{
}

void MainWindow::setupActions()
{
    // File menu actions
    m_newAction = new QAction("&New", this);
    m_newAction->setShortcut(QKeySequence::New);
    connect(m_newAction, &QAction::triggered, this, &MainWindow::newFile);
    
    m_openAction = new QAction("&Open File...", this);
    m_openAction->setShortcut(QKeySequence::Open);
    connect(m_openAction, &QAction::triggered, this, &MainWindow::openFile);
    
    m_openProjectAction = new QAction("Open &Project...", this);
    connect(m_openProjectAction, &QAction::triggered, this, &MainWindow::openProject);
    
    m_saveAction = new QAction("&Save", this);
    m_saveAction->setShortcut(QKeySequence::Save);
    m_saveAction->setEnabled(false);
    connect(m_saveAction, &QAction::triggered, this, &MainWindow::saveFile);
    
    m_saveAsAction = new QAction("Save &As...", this);
    m_saveAsAction->setShortcut(QKeySequence::SaveAs);
    m_saveAsAction->setEnabled(false);
    connect(m_saveAsAction, &QAction::triggered, this, &MainWindow::saveAsFile);
    
    m_exitAction = new QAction("E&xit", this);
    m_exitAction->setShortcut(QKeySequence::Quit);
    connect(m_exitAction, &QAction::triggered, this, &MainWindow::exitApplication);
    
    // Help menu actions
    m_aboutAction = new QAction("&About Velocity", this);
    connect(m_aboutAction, &QAction::triggered, this, &MainWindow::showAbout);
}

void MainWindow::setupMenuBar()
{
    // File menu
    QMenu *fileMenu = menuBar()->addMenu("&File");
    fileMenu->addAction(m_newAction);
    fileMenu->addSeparator();
    fileMenu->addAction(m_openAction);
    fileMenu->addAction(m_openProjectAction);
    fileMenu->addSeparator();
    fileMenu->addAction(m_saveAction);
    fileMenu->addAction(m_saveAsAction);
    fileMenu->addSeparator();
    fileMenu->addAction(m_exitAction);
    
    // Edit menu (placeholder for now)
    QMenu *editMenu = menuBar()->addMenu("&Edit");
    editMenu->addAction("&Undo")->setEnabled(false);
    editMenu->addAction("&Redo")->setEnabled(false);
    editMenu->addSeparator();
    editMenu->addAction("Cu&t")->setEnabled(false);
    editMenu->addAction("&Copy")->setEnabled(false);
    editMenu->addAction("&Paste")->setEnabled(false);
    
    // Run menu (placeholder for now)
    QMenu *runMenu = menuBar()->addMenu("&Run");
    runMenu->addAction("&Run")->setEnabled(false);
    runMenu->addAction("&Debug")->setEnabled(false);
    
    // Help menu
    QMenu *helpMenu = menuBar()->addMenu("&Help");
    helpMenu->addAction(m_aboutAction);
}

void MainWindow::setupUI()
{
    m_stackedWidget = new QStackedWidget(this);
    setCentralWidget(m_stackedWidget);
    
    // Create welcome screen
    m_welcomeScreen = new WelcomeScreen(this);
    connect(m_welcomeScreen, &WelcomeScreen::openFileRequested, this, &MainWindow::openFile);
    connect(m_welcomeScreen, &WelcomeScreen::openProjectRequested, this, &MainWindow::openProject);
    
    // Create text editor
    m_textEditor = new TextEditor(this);
    
    // Add widgets to stacked widget
    m_stackedWidget->addWidget(m_welcomeScreen);
    m_stackedWidget->addWidget(m_textEditor);
    
    // Show welcome screen initially
    m_stackedWidget->setCurrentWidget(m_welcomeScreen);
}

void MainWindow::openFile()
{
    QString fileName = QFileDialog::getOpenFileName(this,
        "Open File", "", "All Files (*.*);;Text Files (*.txt);;C++ Files (*.cpp *.h);;Python Files (*.py)");
    
    if (!fileName.isEmpty()) {
        onFileOpened(fileName);
    }
}

void MainWindow::openProject()
{
    QString dirName = QFileDialog::getExistingDirectory(this,
        "Open Project Directory", "");
    
    if (!dirName.isEmpty()) {
        // For now, just show a message that project opening is not fully implemented
        QMessageBox::information(this, "Project Opening", 
            "Project opening functionality will be enhanced in future versions.\n"
            "For now, you can open individual files from the project directory.");
    }
}

void MainWindow::newFile()
{
    m_currentFilePath.clear();
    m_textEditor->clear();
    m_stackedWidget->setCurrentWidget(m_textEditor);
    m_saveAction->setEnabled(true);
    m_saveAsAction->setEnabled(true);
    setWindowTitle("Velocity - New File");
}

void MainWindow::saveFile()
{
    if (m_currentFilePath.isEmpty()) {
        saveAsFile();
    } else {
        m_textEditor->saveToFile(m_currentFilePath);
        m_isModified = false;
        setWindowTitle(QString("Velocity - %1").arg(QFileInfo(m_currentFilePath).fileName()));
    }
}

void MainWindow::saveAsFile()
{
    QString fileName = QFileDialog::getSaveFileName(this,
        "Save File", "", "All Files (*.*);;Text Files (*.txt);;C++ Files (*.cpp *.h);;Python Files (*.py)");
    
    if (!fileName.isEmpty()) {
        m_currentFilePath = fileName;
        saveFile();
    }
}

void MainWindow::exitApplication()
{
    QApplication::quit();
}

void MainWindow::showAbout()
{
    QMessageBox::about(this, "About Velocity",
        "Velocity Code Editor v1.0.0\n\n"
        "A modern, lightweight code editor built with Qt.\n\n"
        "Features:\n"
        "• File and project management\n"
        "• Clean, intuitive interface\n"
        "• Cross-platform compatibility");
}

void MainWindow::onFileOpened(const QString &filePath)
{
    m_currentFilePath = filePath;
    m_textEditor->loadFromFile(filePath);
    m_stackedWidget->setCurrentWidget(m_textEditor);
    m_saveAction->setEnabled(true);
    m_saveAsAction->setEnabled(true);
    setWindowTitle(QString("Velocity - %1").arg(QFileInfo(filePath).fileName()));
}

void MainWindow::showWelcomeScreen()
{
    m_stackedWidget->setCurrentWidget(m_welcomeScreen);
    setWindowTitle("Velocity");
}
