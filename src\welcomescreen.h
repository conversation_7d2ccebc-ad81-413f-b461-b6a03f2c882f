#ifndef WELCOMESCREEN_H
#define WELCOMESCREEN_H

#include <QWidget>
#include <QPushButton>
#include <QLabel>
#include <QScrollArea>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QFrame>
#include <QTextEdit>

class WelcomeScreen : public QWidget
{
    Q_OBJECT

public:
    explicit WelcomeScreen(QWidget *parent = nullptr);

signals:
    void openFileRequested();
    void openProjectRequested();

private slots:
    void onOpenFileClicked();
    void onOpenProjectClicked();

private:
    void setupUI();
    void setupHeader();
    void setupFileOperations();
    void setupTipsSection();
    QString getTipsContent();
    
    // UI Components
    QVBoxLayout *m_mainLayout;
    QLabel *m_titleLabel;
    QFrame *m_headerFrame;
    QFrame *m_contentFrame;
    QPushButton *m_openFileButton;
    QPushButton *m_openProjectButton;
    QTextEdit *m_tipsTextEdit;
    QScrollArea *m_tipsScrollArea;
};

#endif // WELCOMESCREEN_H
