# Velocity Code Editor

A modern, lightweight code editor built with Qt C++.

## Features

- **Welcome Screen**: Clean, intuitive welcome interface with quick access to file operations
- **File Management**: Open individual files or browse project directories
- **Text Editing**: Basic text editing with syntax-aware file type detection
- **Cross-Platform**: Built with Qt for Windows, macOS, and Linux compatibility
- **Keyboard Shortcuts**: Standard shortcuts for common operations
- **Status Bar**: Real-time information about cursor position and file type

## Screenshots

The application features:
- A professional welcome screen with the "Velocity" branding
- File operation buttons for opening files and projects
- A scrollable tips section with helpful information about current features
- A clean text editor with status bar showing file information and cursor position

## Prerequisites

To build and run Velocity, you need:

1. **Qt 6.x** - Download from [Qt.io](https://www.qt.io/download)
2. **CMake 3.16+** - For building with CMake
3. **C++ Compiler** - MSVC (Windows), GCC (Linux), or Clang (macOS)

### Windows Setup
1. Install Qt 6.x with Qt Creator
2. Install Visual Studio 2019/2022 or Build Tools for Visual Studio
3. Install CMake (if not included with Qt Creator)

### Linux Setup
```bash
# Ubuntu/Debian
sudo apt-get install qt6-base-dev qt6-tools-dev cmake build-essential

# Fedora
sudo dnf install qt6-qtbase-devel qt6-qttools-devel cmake gcc-c++
```

### macOS Setup
```bash
# Using Homebrew
brew install qt@6 cmake
```

## Building the Project

### Option 1: Using Qt Creator (Recommended)
1. Open Qt Creator
2. Open the project by selecting either:
   - `CMakeLists.txt` (for CMake build)
   - `Velocity.pro` (for qmake build)
3. Configure the project with your Qt kit
4. Build and run

### Option 2: Command Line with CMake
```bash
# Configure the project
cmake -B build -S .

# Build the project
cmake --build build

# Run the executable
./build/Velocity        # Linux/macOS
./build/Velocity.exe    # Windows
```

### Option 3: Command Line with qmake
```bash
# Generate Makefile
qmake Velocity.pro

# Build
make                    # Linux/macOS
nmake                   # Windows with MSVC
mingw32-make           # Windows with MinGW
```

## Project Structure

```
Velocity/
├── CMakeLists.txt          # CMake build configuration
├── Velocity.pro            # qmake build configuration
├── README.md              # This file
├── src/
│   ├── main.cpp           # Application entry point
│   ├── mainwindow.h/cpp   # Main application window
│   ├── welcomescreen.h/cpp # Welcome screen widget
│   └── texteditor.h/cpp   # Text editor widget
```

## Usage

### Getting Started
1. Launch Velocity
2. You'll see the welcome screen with:
   - Application title and branding
   - "Open File" and "Open Project" buttons
   - Tips section with helpful information

### File Operations
- **Open File**: Click "Open File" button or use Ctrl+O
- **Open Project**: Click "Open Project" button (currently shows directory browser)
- **New File**: Use Ctrl+N or File menu
- **Save**: Use Ctrl+S or File menu
- **Save As**: Use Ctrl+Shift+S or File menu

### Keyboard Shortcuts
- `Ctrl+N` - New file
- `Ctrl+O` - Open file
- `Ctrl+S` - Save file
- `Ctrl+Shift+S` - Save as
- `Ctrl+Q` - Exit application

## Current Implementation Status

### ✅ Completed Features
- Welcome screen with professional UI
- File opening and saving functionality
- Basic text editor with monospace font
- Menu bar with File, Edit, and Run menus
- Status bar with file info and cursor position
- File type detection
- Keyboard shortcuts
- Cross-platform project structure

### 🚧 Future Enhancements
- Syntax highlighting
- Auto-completion
- Project management
- Find and replace
- Multiple tabs
- Plugin system

## Contributing

This is a foundational implementation of the Velocity code editor. The codebase is structured to be easily extensible for additional features.

## License

This project is provided as-is for educational and development purposes.

## Technical Notes

- Built with Qt 6.x using QtWidgets
- Uses modern C++17 features
- Follows Qt's signal-slot architecture
- Modular design with separate classes for different components
- Cross-platform compatible build system
