#ifndef TEXTEDITOR_H
#define TEXTEDITOR_H

#include <QWidget>
#include <QTextEdit>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QFrame>
#include <QFileInfo>
#include <QTextStream>
#include <QFile>
#include <QMessageBox>
#include <QFont>
#include <QFontMetrics>
#include <QScrollBar>

class LineNumberArea;

class TextEditor : public QWidget
{
    Q_OBJECT

public:
    explicit TextEditor(QWidget *parent = nullptr);
    
    void loadFromFile(const QString &filePath);
    void saveToFile(const QString &filePath);
    void clear();
    bool isModified() const;
    QString getCurrentText() const;
    void setText(const QString &text);

signals:
    void textChanged();
    void modificationChanged(bool modified);

private slots:
    void onTextChanged();
    void updateLineNumberArea();

private:
    void setupUI();
    void setupEditor();
    void setupStatusBar();
    QString detectFileType(const QString &filePath);
    void updateStatusBar();
    
    // UI Components
    QVBoxLayout *m_mainLayout;
    QTextEdit *m_textEdit;
    QFrame *m_statusFrame;
    QLabel *m_statusLabel;
    QLabel *m_positionLabel;
    QLabel *m_fileTypeLabel;
    
    // Editor state
    QString m_currentFilePath;
    bool m_isModified;
    QString m_fileType;
};

#endif // TEXTEDITOR_H
