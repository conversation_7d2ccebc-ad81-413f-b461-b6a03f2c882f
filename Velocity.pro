QT += core widgets

CONFIG += c++17

TARGET = Velocity
TEMPLATE = app

# Source files
SOURCES += \
    src/main.cpp \
    src/mainwindow.cpp \
    src/welcomescreen.cpp \
    src/texteditor.cpp

# Header files
HEADERS += \
    src/mainwindow.h \
    src/welcomescreen.h \
    src/texteditor.h

# Include path
INCLUDEPATH += src

# Output directory
DESTDIR = bin

# Object files directory
OBJECTS_DIR = build/obj
MOC_DIR = build/moc
RCC_DIR = build/rcc
UI_DIR = build/ui

# Application properties
VERSION = 1.0.0
QMAKE_TARGET_COMPANY = "Velocity Code Editor"
QMAKE_TARGET_PRODUCT = "Velocity"
QMAKE_TARGET_DESCRIPTION = "Modern Code Editor"
QMAKE_TARGET_COPYRIGHT = "Copyright 2024"
